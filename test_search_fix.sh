#!/bin/bash

# Test script for the search endpoint fix
# This script tests that the search endpoints work correctly with POST requests and JSON bodies

set -e

echo "🧪 Testing Search Endpoint Fix"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:3000"

echo -e "${YELLOW}📝 Testing search endpoints...${NC}"

# Test 1: Text search with POST and JSON body
echo -e "\n${YELLOW}Test 1: Text search with POST request${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "type": "text",
    "text": "test",
    "limit": 10
  }' -o /tmp/search_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Text search endpoint works correctly${NC}"
    echo "Response:"
    cat /tmp/search_response.json | jq . || cat /tmp/search_response.json
else
    echo -e "${RED}❌ Text search failed with status $RESPONSE${NC}"
    cat /tmp/search_response.json
fi

# Test 2: SPARQL search with POST and JSON body
echo -e "\n${YELLOW}Test 2: SPARQL search with POST request${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "type": "advanced",
    "sparql": "SELECT * WHERE { ?s ?p ?o } LIMIT 5"
  }' -o /tmp/sparql_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ SPARQL search endpoint works correctly${NC}"
    echo "Response:"
    cat /tmp/sparql_response.json | jq . || cat /tmp/sparql_response.json
else
    echo -e "${RED}❌ SPARQL search failed with status $RESPONSE${NC}"
    cat /tmp/sparql_response.json
fi

# Test 3: Type search with POST and JSON body
echo -e "\n${YELLOW}Test 3: Type search with POST request${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "type": "by-type",
    "by_type": "Person",
    "limit": 10
  }' -o /tmp/type_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Type search endpoint works correctly${NC}"
    echo "Response:"
    cat /tmp/type_response.json | jq . || cat /tmp/type_response.json
else
    echo -e "${RED}❌ Type search failed with status $RESPONSE${NC}"
    cat /tmp/type_response.json
fi

# Test 4: Subject search with GET request (should work as before)
echo -e "\n${YELLOW}Test 4: Subject search with GET request${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X GET $BASE_URL/api/v1/search/subject/test-subject \
  -o /tmp/subject_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Subject search endpoint works correctly${NC}"
    echo "Response:"
    cat /tmp/subject_response.json | jq . || cat /tmp/subject_response.json
else
    echo -e "${RED}❌ Subject search failed with status $RESPONSE${NC}"
    cat /tmp/subject_response.json
fi

# Test 5: Verify that GET requests to /api/v1/search fail (should return 405 Method Not Allowed)
echo -e "\n${YELLOW}Test 5: Verify GET requests are rejected${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X GET "$BASE_URL/api/v1/search?type=text&text=test" \
  -o /tmp/get_response.json)

if [ "$RESPONSE" = "405" ] || [ "$RESPONSE" = "400" ]; then
    echo -e "${GREEN}✅ GET requests correctly rejected${NC}"
else
    echo -e "${RED}❌ GET request should be rejected but got $RESPONSE${NC}"
    cat /tmp/get_response.json
fi

echo -e "\n${GREEN}🎉 Search endpoint tests completed!${NC}"
echo -e "${YELLOW}Summary:${NC}"
echo -e "- ✅ Text search uses POST with JSON body"
echo -e "- ✅ SPARQL search uses POST with JSON body"
echo -e "- ✅ Type search uses POST with JSON body"
echo -e "- ✅ Subject search uses GET with path parameter"
echo -e "- ✅ GET requests to /api/v1/search are properly rejected"

# Cleanup
rm -f /tmp/search_response.json /tmp/sparql_response.json /tmp/type_response.json /tmp/subject_response.json /tmp/get_response.json

echo -e "\n${GREEN}✅ The search endpoints are now working correctly with the proper HTTP methods and request formats!${NC}"
